* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  overflow-x: hidden;
  width: 100%;
}
* {
  -webkit-tap-highlight-color: transparent;
}
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}
:focus-visible {
  outline: 3px solid var(--primary-pink);
  outline-offset: 2px;
}
:root {
  --primary-pink: #f4c2c2;
  --soft-pink: #f9e5e5;
  --blush: #f7d7d7;
  --cream: #fdf9f7;
  --charcoal: #2c2c2c;
  --dark-grey: #4a4a4a;
  --light-grey: #8b8b8b;
  --white: #ffffff;
  --accent-gold: #d4af37;
  --font-primary: "Inter", sans-serif;
  --font-display: "Playfair Display", serif;
  --section-padding: 80px 0;
  --container-max: 1200px;
  --transition: all 0.3s ease;
}
html {
  scroll-behavior: smooth;
  scroll-padding-top: 100px;
}
@media (max-width: 768px) {
  html {
    scroll-padding-top: 150px;
  }
}
body {
  font-family: var(--font-primary), system-ui, -apple-system, Segoe UI, Roboto,
    Arial, sans-serif;
  font-size-adjust: 0.5;
  line-height: 1.6;
  color: var(--charcoal);
  background: var(--white);
  font-weight: 400;
}
.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 20px;
}
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
  border-bottom: 1px solid rgba(244, 194, 194, 0.1);
}
.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}
.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 20px;
}
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}
.logo a {
  text-decoration: none;
  display: flex;
  align-items: center;
}
.logo img {
  height: 100px;
  width: auto;
  object-fit: contain;
  max-width: 300px;
  transition: opacity 0.3s ease;
}
.logo a:hover img {
  opacity: 0.8;
}
.pre-animate {
  opacity: 0;
}
.anim-fade-up {
  transform: translateY(30px);
}
.anim-fade-left {
  transform: translateX(-30px);
}
.anim-fade-right {
  transform: translateX(30px);
}
.reveal {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s ease, transform 0.6s ease;
}
.logo-text {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  display: none;
}
.nav-menu {
  display: flex;
  align-items: center;
  gap: 40px;
}
.nav-link {
  text-decoration: none;
  color: var(--charcoal);
  font-weight: 500;
  font-size: 15px;
  transition: var(--transition);
  position: relative;
}
.nav-link:hover {
  color: var(--primary-pink);
}
.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-pink);
  transition: var(--transition);
}
.nav-link:hover::after {
  width: 100%;
}
.book-btn {
  background: var(--primary-pink);
  color: var(--white);
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: var(--transition);
}
.book-btn:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
}
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}
.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--charcoal);
  transition: var(--transition);
  border-radius: 2px;
}
.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}
.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}
.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}
.nav-menu.mobile-active {
  display: flex;
  position: fixed;
  top: 130px;
  left: 0;
  right: 0;
  background: var(--white);
  flex-direction: column;
  padding: 15px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-top: 1px solid rgba(244, 194, 194, 0.3);
  backdrop-filter: blur(10px);
  z-index: 999;
  animation: slideDown 0.3s ease-out;
}
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.nav-menu.mobile-active .nav-link,
.nav-menu.mobile-active .book-btn {
  margin-bottom: 4px;
  text-align: center;
  padding: 12px 20px;
  border-radius: 8px;
  transition: var(--transition);
  font-weight: 500;
  font-size: 16px;
}
.nav-menu.mobile-active .nav-link {
  color: var(--charcoal);
  border: 2px solid transparent;
}
.nav-menu.mobile-active .nav-link:hover,
.nav-menu.mobile-active .nav-link:focus {
  background: var(--soft-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
}
.nav-menu.mobile-active .book-btn {
  margin-bottom: 0;
  background: var(--primary-pink);
  color: var(--white);
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(244, 194, 194, 0.4);
}
.nav-menu.mobile-active .book-btn:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 44, 44, 0.3);
}
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}
body.mobile-menu-open {
  overflow: hidden;
}
.hero {
  min-height: 100svh;
  min-height: 100vh;
  background: linear-gradient(135deg, #fdf9f7 0%, #f9e5e5 50%, #f4c2c2 100%);
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 130px;
}
.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23F4C2C2" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23F4C2C2" opacity="0.1"/><circle cx="40" cy="80" r="0.4" fill="%23F4C2C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}
.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 100px;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 2;
}
.hero-text {
  position: relative;
}
.hero-text::before {
  content: "";
  position: absolute;
  top: -40px;
  left: -20px;
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  border-radius: 2px;
}
.hero-text h1 {
  font-family: var(--font-display);
  font-size: 72px;
  font-weight: 700;
  line-height: 1.05;
  color: var(--charcoal);
  margin-bottom: 32px;
  letter-spacing: -0.02em;
}
.hero-text .highlight {
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-style: italic;
  position: relative;
}
.hero-text .highlight::after {
  content: "";
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-pink), transparent);
  opacity: 0.3;
}
.hero-subtitle {
  font-size: 22px;
  color: var(--dark-grey);
  margin-bottom: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 14px;
}
.hero-text p {
  font-size: 20px;
  color: var(--dark-grey);
  margin-bottom: 48px;
  line-height: 1.7;
  max-width: 520px;
  font-weight: 400;
}
.hero-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 48px;
}
.stat-item {
  text-align: left;
}
.stat-number {
  font-family: var(--font-display);
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
}
.stat-label {
  font-size: 14px;
  color: var(--light-grey);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}
.hero-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
}
.btn-primary {
  background: var(--charcoal);
  color: var(--white);
  padding: 18px 36px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  border: 2px solid var(--charcoal);
  position: relative;
  overflow: hidden;
}
.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}
.btn-primary:hover::before {
  left: 100%;
}
.btn-primary:hover {
  background: var(--primary-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(244, 194, 194, 0.4);
}
.btn-secondary {
  background: transparent;
  color: var(--charcoal);
  padding: 18px 36px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  border: 2px solid var(--charcoal);
  position: relative;
}
.btn-secondary:hover {
  background: var(--charcoal);
  color: var(--white);
  transform: translateY(-2px);
}
.cta-section .btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}
.cta-section .btn-secondary:hover {
  background: var(--white);
  color: var(--charcoal);
  transform: translateY(-2px);
}
.hero-visual {
  position: relative;
  height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.hero-image-main {
  position: relative;
  width: 100%;
  height: 600px;
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 30px 60px rgba(244, 194, 194, 0.3);
  transform: rotate(-2deg);
  transition: var(--transition);
}
.hero-image-main:hover {
  transform: rotate(0deg) scale(1.02);
}
.hero-image-main img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 20px;
}
.hero-image-main::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}
.hero-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}
.floating-element {
  position: absolute;
  background: var(--white);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.floating-element-1 {
  top: 10%;
  right: -10%;
  width: 180px;
  animation: float 6s ease-in-out infinite;
}
.floating-element-2 {
  bottom: 15%;
  left: -15%;
  width: 160px;
  animation: float 8s ease-in-out infinite reverse;
}
.floating-element h4 {
  font-family: var(--font-display);
  font-size: 18px;
  color: var(--charcoal);
  margin-bottom: 8px;
}
.floating-element p {
  font-size: 14px;
  color: var(--light-grey);
  margin: 0;
}
.hero-image-placeholder {
  color: var(--white);
  text-align: center;
  z-index: 2;
  position: relative;
}
.hero-image-placeholder i {
  font-size: 100px;
  margin-bottom: 24px;
  opacity: 0.9;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}
.hero-image-placeholder p {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
.elements-section {
  padding: var(--section-padding);
  background: var(--white);
}
.section-header {
  text-align: center;
  margin-bottom: 80px;
}
.section-title {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.section-subtitle {
  font-size: 18px;
  color: var(--light-grey);
  max-width: 600px;
  margin: 0 auto;
}
.elements-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}
.elements-grid.pyramid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 24px;
}
.elements-grid.pyramid .element-card {
  padding: 32px 20px;
}
@media (max-width: 1024px) {
  .elements-grid.pyramid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
  .elements-grid.pyramid .element-card:nth-child(5) {
    grid-column: 1;
  }
  .elements-grid.pyramid .element-card:nth-child(6) {
    grid-column: 2;
  }
  .elements-grid.pyramid .element-card:nth-child(7) {
    grid-column: 3;
  }
}
@media (max-width: 768px) {
  .elements-grid.pyramid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  .elements-grid.pyramid .element-card {
    grid-column: auto;
  }
  .elements-grid.pyramid .element-card:nth-child(7) {
    grid-column: 1 / -1;
  }
}
.element-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 12px;
  background: var(--white);
  border: 1px solid rgba(44, 44, 44, 0.1);
  transition: var(--transition);
}
.element-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(44, 44, 44, 0.15);
  border-color: var(--charcoal);
}
.element-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border: 2px solid var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}
.element-card:hover .element-icon {
  background: var(--primary-pink);
  border-color: var(--primary-pink);
  transform: scale(1.1);
}
.element-icon svg {
  width: 32px;
  height: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}
.element-card:hover .element-icon svg {
  color: var(--charcoal);
}
.element-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.element-card p {
  color: var(--light-grey);
  font-size: 15px;
  line-height: 1.6;
}
.services-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.services-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}
.services-text h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
  line-height: 1.2;
}
.services-text p {
  font-size: 18px;
  color: var(--dark-grey);
  margin-bottom: 32px;
  line-height: 1.7;
}
.services-text blockquote {
  font-size: 22px;
  color: var(--dark-grey);
  font-style: italic;
  line-height: 1.6;
  margin: 32px 0;
  padding-left: 24px;
  border-left: 4px solid var(--primary-pink);
  position: relative;
}
.services-list {
  list-style: none;
  margin-bottom: 40px;
}
.services-list li {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  color: var(--charcoal);
}
.services-list svg {
  color: var(--primary-pink);
  margin-right: 12px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}
.services-image {
  height: 500px;
  background: var(--white);
  border-radius: 12px;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.services-image picture {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
  width: 100%;
  height: 100%;
}
.services-image img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}
.services-grid-section {
  padding: var(--section-padding);
  background: var(--white);
}
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}
.service-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: var(--transition);
}
.service-card-link:hover {
  text-decoration: none;
  color: inherit;
}
.service-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  transform: scaleX(0);
  transition: var(--transition);
}
.service-card:hover::before,
.service-card-link:hover .service-card::before {
  transform: scaleX(1);
}
.service-card:hover,
.service-card-link:hover .service-card {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}
.service-card.featured {
  border: 2px solid var(--primary-pink);
  background: linear-gradient(135deg, var(--white) 0%, var(--soft-pink) 100%);
}
.service-card.featured::before {
  transform: scaleX(1);
}
.service-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: var(--transition);
}
.service-card:hover .service-icon,
.service-card-link:hover .service-card .service-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}
.service-card.featured .service-icon {
  background: var(--primary-pink);
}
.service-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}
.service-card:hover .service-icon i,
.service-card-link:hover .service-card .service-icon i,
.service-card.featured .service-icon i {
  color: var(--white);
}
.service-card h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
  line-height: 1.2;
  min-height: 2.6em;
}
.service-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
  flex-grow: 1;
  min-height: 120px;
}

.service-features {
  list-style: none;
  margin-bottom: 24px;
}
.service-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}
.service-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}
.service-price {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-pink);
  text-align: right;
}
.process-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}
.process-step {
  text-align: center;
  position: relative;
}
.step-number {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 700;
  color: var(--white);
  box-shadow: 0 8px 20px rgba(244, 194, 194, 0.4);
}
.process-step h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.process-step p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}
.cta-section {
  padding: var(--section-padding);
  background: var(--charcoal);
  text-align: center;
}
.cta-content h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 20px;
}
.cta-content p {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}
.page-header {
  padding: 140px 0 80px;
  background: linear-gradient(135deg, var(--soft-pink) 0%, var(--cream) 100%);
  text-align: center;
  position: relative;
}
.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23F4C2C2" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23F4C2C2" opacity="0.1"/><circle cx="40" cy="80" r="0.4" fill="%23F4C2C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}
.page-header-content {
  position: relative;
  z-index: 2;
}
.page-header h1 {
  font-family: var(--font-display), Georgia, "Times New Roman", serif;
  font-size: 56px;
  font-weight: 700;
  color: var(--charcoal);
  margin-bottom: 20px;
  letter-spacing: -0.02em;
  font-size-adjust: 0.46;
}
.page-header .highlight {
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-style: italic;
}
.page-header p {
  font-size: 20px;
  color: var(--dark-grey);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.services-grid-section {
  padding: var(--section-padding);
  background: var(--white);
}
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}
.service-card h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
  line-height: 1.2;
  min-height: 2.6em;
}
.service-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
  flex-grow: 1;
  min-height: 120px;
}
.service-features {
  list-style: none;
  margin-bottom: 24px;
}
.service-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
  line-height: 1.4;
}
.service-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}
.service-price {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-pink);
  text-align: right;
}

/* Ensure Services page cards are perfectly centered (all breakpoints) */
.services-grid .service-card {
  align-items: center;
  text-align: center;
  display: flex;
  flex-direction: column;
}
.services-grid .service-icon {
  margin: 0 auto 24px;
}
.services-grid .service-card h3,
.services-grid .service-card p,
.services-grid .service-features,
.services-grid .service-price {
  text-align: center;
}
.services-grid .service-features {
  display: inline-grid;
  grid-auto-rows: auto;
  row-gap: 12px;
  justify-items: center;
  padding-left: 0;
  margin-left: auto;
  margin-right: auto;
}
.services-grid .service-features li {
  justify-content: center;
}
.services-grid .service-price {
  margin-top: 8px;
  text-align: center;
}

.process-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Absolute centering rail: forces everything to the exact midline */
.services-grid .service-card > .service-icon,
.services-grid .service-card > h3,
.services-grid .service-card > p,
.services-grid .service-card > .service-features,
.services-grid .service-card > .service-price {
  align-self: center;
}

/* Bullet rows: perfectly symmetrical columns around the center */
.services-grid .service-features li {
  display: grid;
  grid-template-columns: 24px auto 24px; /* icon | text | spacer */
  gap: 8px;
  align-items: center;
  justify-items: center;
  text-align: center;
}
.services-grid .service-features li i {
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.services-grid .service-features li::after {
  content: "";
  width: 24px;
  height: 1px;
}

/* Titles/descriptions get a consistent max width so every card wraps similarly */
.services-grid .service-card h3,
.services-grid .service-card p,
.services-grid .service-features,
.services-grid .service-price {
  max-width: 320px;
  margin-left: auto;
  margin-right: auto;
}

  @media (min-width: 1025px) {
    .services-grid .service-card h3,
    .services-grid .service-card p,
    .services-grid .service-features,
    .services-grid .service-price {
      max-width: 360px;
    }
  }

  gap: 40px;
  margin-top: 60px;
}
.process-step {
  text-align: center;
  position: relative;
}
.step-number {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 700;
  color: var(--white);
  box-shadow: 0 8px 20px rgba(244, 194, 194, 0.4);
}
.process-step h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.process-step p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}
.cta-section {
  padding: var(--section-padding);
  background: var(--charcoal);
  text-align: center;
}
.cta-content h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 20px;
}
.cta-content p {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}
.nav-link.active {
  color: var(--primary-pink);
}
.nav-link.active::after {
  width: 100%;
}
.about-hero {
  padding: var(--section-padding);
  background: var(--white);
}
.about-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 80px;
  align-items: center;
}
.about-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.3);
  transition: var(--transition);
  height: 500px;
  width: 100%;
}
.about-image:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 70px rgba(244, 194, 194, 0.4);
}
.about-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  object-position: center;
  display: block;
}
.about-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}
.about-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 24px;
}
.credentials {
  margin-top: 40px;
}
.credential-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--soft-pink);
  border-radius: 12px;
  transition: var(--transition);
}
.credential-item:hover {
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(244, 194, 194, 0.2);
}
.credential-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.credential-icon i {
  font-size: 24px;
  color: var(--white);
}
.credential-text h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 4px;
}
.credential-text p {
  font-size: 14px;
  color: var(--light-grey);
  margin: 0;
}
.experience-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 60px;
}
.experience-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}
.experience-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}
.experience-number {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
  margin-bottom: 16px;
}
.experience-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}
.experience-card p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}
.philosophy-section {
  padding: var(--section-padding);
  background: var(--white);
}
.philosophy-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 80px;
  align-items: center;
}
.philosophy-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}
.philosophy-text > p {
  font-size: 20px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 40px;
  font-style: italic;
  position: relative;
  padding-left: 20px;
}
.philosophy-points {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.philosophy-point {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}
.philosophy-section .services-list {
  margin-bottom: 0;
}
.philosophy-section .services-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  font-size: 16px;
  color: var(--charcoal);
}
.philosophy-section .services-list svg {
  color: var(--dark-grey);
  margin-right: 16px;
  margin-top: 4px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}
.philosophy-section .services-list h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 8px;
  margin-top: 0;
}
.philosophy-section .services-list p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}
.philosophy-image {
  height: 500px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.3);
}
.philosophy-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}
.specialties-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.specialties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}
#philosophy .philosophy-hero {
  display: grid;
  grid-template-columns: 1.05fr 1fr;
  gap: 80px;
  align-items: center;
}
.philosophy-hero-image {
  position: relative;
  height: 520px;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 24px 60px rgba(244, 194, 194, 0.35);
}
.philosophy-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}
.philosophy-hero-content {
  padding: 16px 0;
}
.philosophy-hero-content h3 {
  font-family: var(--font-display);
  font-size: 40px;
  font-weight: 600;
  color: var(--charcoal);
  margin: 0 0 16px 0;
}
.philosophy-hero-content p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.8;
  margin-bottom: 18px;
}
.feature-list {
  list-style: none;
  padding: 0;
  margin: 24px 0 0 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}
.feature-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: var(--charcoal);
}
.feature-list i {
  color: var(--primary-pink);
  font-size: 18px;
}
@media (max-width: 1024px) {
  #philosophy .philosophy-hero {
    gap: 40px;
  }
  .philosophy-hero-image {
    height: 460px;
  }
}
@media (max-width: 768px) {
  #philosophy .philosophy-hero {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }
  .philosophy-hero-image {
    height: 360px;
    order: 1;
  }
  .philosophy-hero-content {
    order: 2;
  }
  .feature-list {
    text-align: left;
    max-width: 720px;
    margin-left: auto;
    margin-right: auto;
  }
}
#philosophy .philosophy-text h2 {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}
#philosophy .philosophy-text blockquote {
  text-align: center;
  margin: 0 auto 32px;
  max-width: 820px;
  color: var(--dark-grey);
  font-style: italic;
  line-height: 1.8;
}
.specialty-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}
.specialty-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}
.specialty-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}
.specialty-icon i {
  font-size: 32px;
  color: var(--white);
}
.specialty-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.specialty-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
}
.sustainability-stats {
  padding: var(--section-padding);
  background: var(--white);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}
.stat-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
}
.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}
.stat-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}
.stat-card:hover .stat-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}
.stat-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}
.stat-card:hover .stat-icon i {
  color: var(--white);
}
.stat-number {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
  margin-bottom: 8px;
}
.stat-label {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.stat-card p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}
.sustainable-practices {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}
.practice-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  height: 100%;
}
.practice-card {
  text-align: center;
}
.practice-features {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  margin: 0 auto;
}
.practice-features li {
  justify-content: center;
}
.practice-features i {
  margin-right: 8px;
}
.practice-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}
.practice-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}
.practice-icon i {
  font-size: 32px;
  color: var(--white);
}
.practice-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.practice-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
  flex-grow: 1;
  min-height: 72px;
}
.practice-features {
  list-style: none;
}
.practice-features li {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: max-content;
  margin: 0 auto 12px;
  font-size: 15px;
  color: var(--charcoal);
  line-height: 1.4;
  width: fit-content;
  text-align: center;
  transform: translateX(-6px);
}
.practice-features i {
  color: var(--primary-pink);
  margin-right: 0;
  font-size: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}
.ponytail-donation {
  padding: var(--section-padding);
  background: var(--white);
}
.donation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}
.donation-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}
.donation-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}
.donation-requirements h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 20px;
}
.donation-requirements ul {
  list-style: none;
  margin-bottom: 32px;
}
.donation-requirements li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--charcoal);
  line-height: 1.4;
}
.donation-requirements i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}
.donation-image {
  height: 400px;
  background: var(--soft-pink);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.donation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}
.partnership-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.partnership-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}
.partnership-image {
  height: 400px;
  background: var(--white);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.partnership-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}
.partnership-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}
.partnership-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}
.partnership-benefits h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 20px;
}
.partnership-benefits ul {
  list-style: none;
  margin-bottom: 32px;
}
.partnership-benefits li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--charcoal);
  line-height: 1.4;
}
.partnership-benefits i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}
.contact-info {
  padding: var(--section-padding);
  background: var(--white);
}
.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}
.contact-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
}
.contact-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}
.contact-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}
.contact-card:hover .contact-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}
.contact-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}
.contact-card:hover .contact-icon i {
  color: var(--white);
}
.contact-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}
.contact-card p {
  font-size: 16px;
  color: var(--charcoal);
  margin-bottom: 8px;
  line-height: 1.5;
}
.contact-card p a {
  color: var(--primary-pink);
  text-decoration: none;
  font-weight: 600;
}
.contact-card p a:hover {
  text-decoration: underline;
}
.contact-card span {
  font-size: 14px;
  color: var(--light-grey);
}
.booking-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.booking-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 80px;
  align-items: center;
}
.booking-content.single-col {
  grid-template-columns: 1fr;
}
.booking-text .booking-image {
  margin: 20px auto 30px;
  max-width: 560px;
}
.booking-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}
.booking-text > p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 40px;
}
.booking-benefits {
  margin-bottom: 40px;
}
.booking-benefit {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 24px;
}
.booking-benefit i {
  font-size: 24px;
  color: var(--primary-pink);
  margin-top: 4px;
}
.booking-benefit h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 8px;
}
.booking-benefit p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}
.booking-cta {
  background: var(--white);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}
.booking-cta h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}
.booking-cta p {
  font-size: 16px;
  color: var(--dark-grey);
  margin-bottom: 24px;
}
.booking-cta a {
  color: var(--primary-pink);
  text-decoration: none;
  font-weight: 600;
}
.booking-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.booking-image {
  height: 500px;
  background: var(--white);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.booking-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}
.location-section {
  padding: var(--section-padding);
  background: var(--white);
}
.location-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin-top: 60px;
}
.location-info h3 {
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.location-info > p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}
.location-features {
  margin-bottom: 32px;
}
.location-feature {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}
.location-feature i {
  font-size: 20px;
  color: var(--primary-pink);
}
.location-feature span {
  font-size: 16px;
  color: var(--charcoal);
}
.directions h4 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.directions p {
  font-size: 15px;
  color: var(--dark-grey);
  margin-bottom: 8px;
}
.location-map {
  height: 400px;
  background: var(--soft-pink);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.location-map img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}
.faq-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}
.faq-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 60px;
}
.faq-item {
  background: var(--white);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}
.faq-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}
.faq-item h3 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}
.faq-item p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}
.footer {
  background: var(--charcoal);
  color: var(--white);
  padding: 60px 0 30px;
}
.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 60px;
  margin-bottom: 40px;
}
.footer-section h3 {
  font-family: var(--font-display);
  font-size: 24px;
  margin-bottom: 20px;
  color: var(--primary-pink);
}
.footer-section p,
.footer-section a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  margin-bottom: 8px;
  display: block;
  transition: var(--transition);
}
.footer-section a:hover {
  color: var(--primary-pink);
}
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}
.desktop-only {
  display: block;
}
.mobile-only {
  display: none;
}
@media (max-width: 1024px) {
  .hero-content {
    gap: 60px;
  }
  .hero-text h1 {
    font-size: 64px;
  }
  .floating-element-1,
  .floating-element-2 {
    display: none;
  }
  .elements-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  .services-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }
  .location-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }
  .location-info {
    order: 1;
  }
  .location-map {
    order: 2;
    height: 360px;
  }
  .about-content {
    gap: 60px;
  }
  .container {
    padding: 0 30px;
  }
}
@media (max-width: 768px) {
  .hero {
    padding-top: 110px;
    padding-bottom: 16px;
  }
  .hero-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }
  .hero-text::before {
    left: 50%;
    transform: translateX(-50%);
  }
  .hero-text h1 {
    font-size: 52px;
  }
  .hero-stats {
    justify-content: center;
  }
  /* Reorder hero for mobile: text -> image -> stats -> buttons */
  .hero-content {
    display: flex;
    flex-direction: column;
  }
  .hero-text {
    display: contents;
  }
  .hero-text h1 {
    order: 1;
  }
  .hero-text p {
    order: 2;
  }
  .hero-visual {
    order: 3;
  }
  .hero-stats {
    order: 4;
  }
  .hero-stats {
    margin-bottom: 16px;
  }
  .hero-buttons {
    order: 5;
    margin-top: -15px;
  }

  .hero-visual {
    height: 500px;
  }
  .hero-image-main {
    height: 450px;
    transform: rotate(0deg);
  }
  .nav-menu.mobile-active {
    top: 110px;
  }
  .page-header {
    padding: 120px 0 60px;
  }
  .elements-grid {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }
  .services-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 40px;
    text-align: center;
  }
  #services .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #services .services-text {
    display: contents;
  }
  #services .services-text h2 {
    order: 1;
  }
  #services .services-text > p {
    order: 2;
  }
  #services .services-image {
    order: 3;
    height: 300px;
    align-self: center;
    margin: 0 auto;
  }
  #services .services-list {
    order: 4;
  }
  #services .btn-primary {
    order: 5;
    align-self: center;
  }
  #about .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #about .services-text {
    display: contents;
  }
  #about .services-text h2 {
    order: 1;
  }
  #about .services-text > p {
    order: 2;
  }
  #about .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #about .services-list {
    order: 4;
  }
  #about .btn-primary {
    order: 5;
    align-self: center;
  }
  #philosophy .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #philosophy .services-text {
    display: contents;
  }
  #philosophy .services-text > p {
    order: 1;
  }
  #philosophy .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #philosophy .services-list {
    order: 4;
  }
  #sustainability .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #sustainability .services-text {
    display: contents;
  }
  #sustainability .services-text h2 {
    order: 1;
  }
  #sustainability .services-text > p {
    order: 2;
  }
  #sustainability .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #sustainability .services-list {
    order: 4;
  }
  #sustainability .btn-primary {
    order: 5;
    align-self: center;
  }
  #contact .services-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  #contact .services-image {
    order: 1;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #contact .services-text {
    order: 2;
  }
  .services-text {
    order: 1;
    width: 100%;
  }
  .services-image {
    order: 2;
    height: 300px;
    width: 100%;
  }
  .services-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
  }
  .philosophy-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  .philosophy-image {
    height: 360px;
  }
  .philosophy-image img {
    height: 100%;
  }
  .philosophy-text {
    order: 1;
  }
  .philosophy-image {
    order: 2;
  }
  .philosophy-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .philosophy-text {
    display: contents;
  }
  .philosophy-text h2 {
    order: 1;
  }
  .philosophy-text > p {
    order: 2;
  }
  .philosophy-image {
    order: 3;
  }
  .philosophy-points {
    order: 4;
  }
  .philosophy-point {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }
  .point-icon svg {
    margin: 0 0 6px 0;
  }
  .donation-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  .donation-text {
    display: contents;
  }
  .donation-text h2 {
    order: 1;
  }
  .donation-text p {
    order: 2;
  }
  .donation-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  .donation-requirements {
    order: 4;
  }
  .donation-requirements ul {
    text-align: center;
    padding-left: 0;
    margin-left: auto;
    margin-right: auto;
  }
  .donation-requirements li {
    justify-content: center;
  }
  .donation-content .btn-primary {
    order: 5;
    align-self: center;
  }
  .location-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 40px;
    text-align: center;
    margin-top: 24px;
  }
  .location-map {
    order: 1 !important;
    height: 320px;
  }
  .partnership-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  .partnership-text {
    display: contents;
  }
  .partnership-text h2 {
    order: 1;
  }
  .partnership-text > p {
    order: 2;
  }
  .partnership-image {
    order: 3;
    height: 320px;
  }
  .partnership-benefits {
    order: 4;
  }
  .partnership-text .btn-secondary {
    order: 5;
    align-self: center;
  }
  .location-info {
    order: 2 !important;
    display: block;
  }
  .directions {
    order: initial;
  }
  .location-features {
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }
  .location-feature {
    justify-content: center;
  }
  .elements-section {
    padding: 60px 0;
  }
  .section-header {
    margin-bottom: 40px;
  }
  .section-title {
    font-size: 32px;
    margin-bottom: 16px;
  }
  .section-subtitle {
    font-size: 16px;
    line-height: 1.5;
  }
  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  .about-image {
    height: 400px;
  }
  .faq-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .booking-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  .booking-benefit {
    gap: 2px !important;
    justify-content: center !important;
    align-items: center !important;
  }
  .booking-benefit i {
    margin-top: 0px !important;
    margin-right: 4px !important;
    flex-shrink: 0;
    font-size: 18px !important;
  }
  .booking-benefit div {
    text-align: center;
  }
  .booking-text h2 {
    font-size: 36px;
  }
  .booking-cta {
    padding: 24px;
  }
  .booking-cta h3 {
    font-size: 24px;
  }
  .booking-buttons {
    flex-direction: column;
    gap: 12px;
  }
  .booking-image {
    height: 300px;
  }
  .mobile-only {
    display: block;
  }
  .desktop-only {
    display: none;
  }
  .nav-menu {
    display: none;
  }
  .mobile-menu-toggle {
    display: flex;
  }
  .logo img {
    height: 80px;
    max-width: 250px;
  }
  .floating-element-1,
  .floating-element-2 {
    display: none;
  }
  .container {
    padding: 0 20px;
  }
  .section-header {
    margin-bottom: 50px;
  }
  .section-title {
    font-size: 36px;
  }
  .section-subtitle {
    font-size: 16px;
  }
  .services-text,
  .about-text {
    text-align: center;
  }
  .services-text h2,
  .about-text h2 {
    font-size: 36px;
  }
  .service-card {
    text-align: center;
  }
  .service-card h3,
  .service-card p,
  .service-features,
  .service-price {
    text-align: center;
  }
  .service-features li {
    justify-content: center;
  }
  .service-icon {
    margin: 0 auto 24px;
  }
  /* Strong visual centering for Services cards (mobile only) */
  .service-card {
    align-items: center;
  }
  .service-card h3,
  .service-card p,
  .service-features,
  .service-price {
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
  }
  .service-features {
    width: 100%;
  }
  .service-features li {
    width: 100%;
    text-align: center;
    display: grid !important;
    grid-template-columns: 20px auto 20px;
    gap: 8px;
    align-items: center;
    justify-items: center;
  }
  .service-features li i {
    margin-right: 0;
    justify-self: end;
  }
  .service-features li::after {
    content: "";
    width: 20px;
    height: 1px;
  }

  .services-list {
    text-align: center;
    max-width: 320px;
    margin: 0 auto 30px;
  }
  .services-list li {
    margin-bottom: 14px;
    font-size: 15px;
    line-height: 1.4;
    justify-content: center;
    gap: 0;
  }
  .services-list svg {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0;
  }
}
@media (max-width: 480px) {
  .hero {
    padding-top: 100px;
    padding-bottom: 16px;
  }
  .hero-text h1 {
    font-size: 42px;
    line-height: 1.1;
  }
  .hero-text p {
    font-size: 18px;
    margin-bottom: 32px;
  }
  .nav-menu.mobile-active {
    top: 100px;
    padding: 12px;
  }
  .nav-menu.mobile-active .nav-link,
  .nav-menu.mobile-active .book-btn {
    margin-bottom: 3px;
    padding: 10px 16px;
  }
  .page-header {
    padding: 110px 0 50px;
  }
  .hero-stats {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }
  .stat-item {
    text-align: center;
  }
  .hero-buttons {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }
  .btn-primary,
  .btn-secondary {
    width: 100%;
    text-align: center;
    padding: 16px 24px;
  }
  .hero-visual {
    height: 400px;
  }
  .hero-image-main {
    height: 350px;
  }
  .elements-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .element-card {
    padding: 30px 20px;
  }
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  .section-title {
    font-size: 32px;
  }
  .services-text h2,
  .about-text h2 {
    font-size: 32px;
    line-height: 1.2;
  }
  .services-text p,
  .about-text p {
    font-size: 16px;
  }
  .services-image {
    height: 250px;
    margin-top: 20px;
  }
  .logo img {
    height: 70px;
    max-width: 200px;
  }
  .navbar {
    padding: 12px 0;
  }
  .container {
    padding: 0 15px;
  }
  .section-header {
    margin-bottom: 40px;
  }
  .element-card h3 {
    font-size: 20px;
  }
  .element-card p {
    font-size: 14px;
  }
  .faq-item {
    padding: 24px 20px;
  }
  .faq-item h3 {
    font-size: 18px;
  }
  .faq-item p {
    font-size: 14px;
  }
}
@media (max-width: 320px) {
  .hero-text h1 {
    font-size: 36px;
  }
  .hero-text p {
    font-size: 16px;
  }
  .section-title {
    font-size: 28px;
  }
  .services-text h2,
  .about-text h2 {
    font-size: 28px;
  }
  .services-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 30px;
  }
  .services-list {
    max-width: 280px;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }
  .services-list li {
    font-size: 14px;
    margin-bottom: 12px;
    justify-content: center;
  }
  .services-image {
    height: 200px;
  }
  .container {
    padding: 0 12px;
  }
  .logo img {
    height: 60px;
    max-width: 180px;
  }
  .btn-primary,
  .btn-secondary {
    padding: 14px 20px;
    font-size: 14px;
  }
  .element-card {
    padding: 24px 16px;
  }
  .faq-item {
    padding: 20px 16px;
  }
  .booking-text h2 {
    font-size: 28px;
  }
  .booking-cta {
    padding: 20px;
  }
  .booking-cta h3 {
    font-size: 20px;
  }
  .booking-image {
    height: 250px;
  }
}
.scroll-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--primary-pink);
  border: none;
  border-radius: 50%;
  color: var(--white);
  font-size: 18px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 1000;
  display: none;
}
.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.scroll-to-top:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
@media (min-width: 769px) {
  .service-card-link {
    height: 100%;
  }
  .service-card {
    height: 100%;
    min-height: 500px;
    text-align: center;
  }
  .service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px auto;
  }
  .service-card h3,
  .service-card p,
  .service-features,
  .service-price {
    text-align: center;
  }
  .service-features {
    flex-grow: 0;
    margin-top: auto;
  }
  .service-features li {
    justify-content: center;
  }
  .service-price {
    margin-top: 16px;
    text-align: center;
  }
}
@media (max-width: 768px) {
  .scroll-to-top {
    display: block;
  }
}
@media (max-width: 768px) {
  #meet-vic .services-content {
    display: flex !important;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #meet-vic .services-text {
    display: contents;
  }
  #meet-vic .services-text h2 {
    order: 1;
  }
  #meet-vic .services-text > p:nth-of-type(1) {
    order: 2;
  }
  #meet-vic .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #meet-vic blockquote {
    order: 4;
  }
  #meet-vic .services-text > p:nth-of-type(2) {
    order: 5;
  }
  #meet-vic .services-text > p:nth-of-type(3) {
    order: 6;
  }


/* Global: muted icon helper to centralize inline filter styling */
.icon-muted { filter: grayscale(1) brightness(0.3); }

/* Global: unify mobile navbar to solid white for consistent status bar */
@media (max-width: 1024px) {
  .navbar { background: #ffffff !important; backdrop-filter: none; }
}

/* About page: mobile-only reorder and list layout (moved from inline <style>) */
@media (max-width: 768px) {
  .philosophy-section .philosophy-content {
    display: grid;
    grid-template-areas: "title" "desc" "img" "list";
    row-gap: 1.25rem;
  }
  .philosophy-section .philosophy-text { display: contents; }
  .philosophy-section .philosophy-text > h2 { grid-area: title; text-align: center; }
  .philosophy-section .philosophy-text > p {
    grid-area: desc; text-align: center !important; margin: 0 auto; max-width: 36ch;
    box-sizing: border-box; text-indent: -0.5ch; padding-left: 0.5ch !important; padding-right: 0.5ch !important; width: auto; justify-self: center;
  }
  .philosophy-section .philosophy-image { grid-area: img; }
  .philosophy-section .services-list { grid-area: list; padding-left: 0; }
  .philosophy-section .services-list li {
    display: grid; grid-template-columns: 1fr auto auto 1fr;
    grid-template-areas: "spacer-left icon title spacer-right" "desc desc desc desc";
    column-gap: 0.5rem; row-gap: 0.25rem; align-items: center; list-style: none;
  }
  .philosophy-section .services-list li > svg { grid-area: icon; width: 18px; height: 18px; justify-self: end; }
  .philosophy-section .services-list li > div { display: contents; }
  .philosophy-section .services-list li > div > h4 { grid-area: title; margin: 0; display: inline-flex; align-items: center; text-align: center; justify-content: center; justify-self: center; }
  .philosophy-section .services-list li > div > p { grid-area: desc; margin: 0; text-align: center; justify-self: center; max-width: 34ch; }
}

/* Contact page: booking benefit layout (moved from inline <style>) */
.booking-benefit { display: flex; flex-direction: column; align-items: flex-start; gap: 8px; }
.booking-benefit .benefit-title { display: inline-flex; align-items: center; gap: 6px; margin: 0 0 8px 0; }
.booking-benefit i { margin-top: 0; }
@media (max-width: 1024px) { .booking-benefit .benefit-title { justify-content: center; } }

    order: 6;
  }
  #meet-vic .btn-secondary {
    order: 7;
    align-self: center;
  }
}
